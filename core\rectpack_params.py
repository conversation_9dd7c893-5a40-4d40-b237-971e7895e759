#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
RectPack算法参数统一模块
第二步：统一测试模式和生产模式的基础参数
"""

import logging

log = logging.getLogger(__name__)

class RectPackParams:
    """RectPack算法统一参数类 - 确保测试模式和生产模式完全一致"""

    def __init__(self):
        # 核心参数 - 确保测试和生产模式完全一致
        self.rotation_enabled = True
        self.spacing_px = 1

        # 排序策略 - 使用最简单的面积排序
        self.sort_by_area = True
        self.sort_strategy = 'AREA'  # 统一排序策略

        # 装箱算法 - 统一使用Best Short Side Fit
        self.pack_algorithm = 'BSSF'

        # 容器配置 - 统一处理
        self.bin_selection_strategy = 0

        log.info("RectPack统一参数初始化完成 - 测试和生产模式参数已统一")

    def get_rectpack_params(self):
        """获取RectPack库的参数 - 测试和生产模式统一"""
        return {
            'rotation': self.rotation_enabled
        }

    def get_sort_function(self):
        """获取统一的排序函数 - 测试和生产模式统一使用"""
        def sort_by_area_desc(rect_list):
            """按面积降序排列 - 测试和生产模式统一使用"""
            return sorted(rect_list, key=lambda rect: rect[0] * rect[1], reverse=True)

        return sort_by_area_desc

    def get_unified_config(self):
        """获取统一配置 - 确保测试和生产模式完全一致"""
        return {
            'rotation_enabled': self.rotation_enabled,
            'spacing_px': self.spacing_px,
            'sort_strategy': self.sort_strategy,
            'pack_algorithm': self.pack_algorithm,
            'bin_selection_strategy': self.bin_selection_strategy,
            'sort_by_area': self.sort_by_area
        }

    def apply_to_arranger(self, arranger):
        """将统一参数应用到排列器 - 确保参数一致性"""
        if hasattr(arranger, 'rotation_enabled'):
            arranger.rotation_enabled = self.rotation_enabled
        if hasattr(arranger, 'spacing_px'):
            arranger.spacing_px = self.spacing_px
        if hasattr(arranger, 'sort_strategy'):
            arranger.sort_strategy = self.sort_strategy
        if hasattr(arranger, 'pack_algorithm'):
            arranger.pack_algorithm = self.pack_algorithm

        log.info("统一参数已应用到排列器")

    def log_params(self):
        """记录当前参数"""
        log.info("当前RectPack统一参数:")
        log.info(f"  旋转启用: {self.rotation_enabled}")
        log.info(f"  图片间距: {self.spacing_px}px")
        log.info(f"  排序策略: {self.sort_strategy} (按面积降序)")
        log.info(f"  装箱算法: {self.pack_algorithm}")
        log.info(f"  容器策略: {self.bin_selection_strategy}")

# 全局参数实例
unified_params = RectPackParams()
